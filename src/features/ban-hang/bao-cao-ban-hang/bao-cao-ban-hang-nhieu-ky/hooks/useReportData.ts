import { useState, useCallback } from 'react';
import { generateFirstTwoColumnHeaders, generateThirdColumnHeader } from '../utils/dateCalculations';
import { generateMockSalesReportData, generateSummaryRow, SalesReportItem } from '../mock-data/data';
import { TableData } from '@/components/custom/arito/data-tables/types';
import { initialSearchValues } from '../schema';
import { SearchFormValues } from '../schema';
import api from '@/lib/api';

// Report data hook return type
export interface UseReportDataReturn {
  initialSearchDialogOpen: boolean;
  editPrintTemplateDialogOpen: boolean;
  showTable: boolean;
  searchParams: SearchFormValues;
  tables: TableData[];
  isLoading: boolean;
  error: Error | null;

  // Event handlers
  handleInitialSearchClose: () => void;
  handleInitialSearch: (data: SearchFormValues) => void;
  handleSearchClick: () => void;
  handleEditPrintTemplateClick: () => void;
  handleClosePrintTemplateDialog: () => void;
  handleSavePrintTemplate: (template: any) => void;
  handleRowClick: (row: any) => void;
  handleRefreshClick: () => void;
  handleFixedColumnsClick: () => void;
  handleExportDataClick: () => void;
}

/**
 * Main hook for managing sales report data and UI state
 *
 * This hook follows the exact pattern used in bang-ke-chung-tu feature
 * and provides comprehensive state management for the multi-period sales report feature.
 */
export function useReportData(): UseReportDataReturn {
  // UI State Management
  const [initialSearchDialogOpen, setInitialSearchDialogOpen] = useState(true);
  const [editPrintTemplateDialogOpen, setEditPrintTemplateDialogOpen] = useState(false);
  const [showTable, setShowTable] = useState(false);
  const [searchParams, setSearchParams] = useState<SearchFormValues>(initialSearchValues);
  const [selectedRow, setSelectedRow] = useState<any | null>(null);

  // Data State Management
  const [tables, setTables] = useState<TableData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Generate dynamic column headers based on report option and analysis period
  const generateDynamicColumns = useCallback((searchParams?: SearchFormValues) => {
    if (!searchParams?.bao_cao_theo || !searchParams?.phan_tich_theo) {
      // Return default 3 columns if no search params
      return [
        { field: 'ma', headerName: 'Mã', width: 150 },
        { field: 'ten', headerName: 'Tên', width: 300, flex: 1 },
        {
          field: 'sl',
          headerName: 'Số lượng',
          width: 120,
          type: 'number' as const,
          align: 'right' as const,
          headerAlign: 'right' as const
        }
      ];
    }

    // Generate headers based on report option and analysis type
    const [firstHeader, secondHeader] = generateFirstTwoColumnHeaders(searchParams.bao_cao_theo);
    const thirdHeader = generateThirdColumnHeader(searchParams.phan_tich_theo);

    return [
      {
        field: 'ma',
        headerName: firstHeader,
        width: 150
      },
      {
        field: 'ten',
        headerName: secondHeader,
        width: 300,
        flex: 1
      },
      {
        field: 'sl',
        headerName: thirdHeader,
        width: 120,
        type: 'number' as const,
        align: 'right' as const,
        headerAlign: 'right' as const
      }
    ];
  }, []);

  const transformDataForPeriods = useCallback((data: SalesReportItem[], params?: SearchFormValues) => {
    if (!params?.phan_tich_theo || !params?.so_ky_phan_tich) {
      return data;
    }

    // Transform the data to match the backend API structure: ["ma", "ten", "sl"]
    return data.map(item => ({
      id: item.id,
      ma: item.ma,
      ten: item.ten,
      sl: item.sl,
      disabled: item.disabled
    }));
  }, []);

  const getTableTitle = useCallback((params?: SearchFormValues): string => {
    if (!params) return 'Báo cáo bán hàng nhiều kỳ';

    const analysisType = params.phan_tich_theo || 'month';
    const periodCount = params.so_ky_phan_tich || 12;
    const fromDate = params.ngay_tu || '';
    const toDate = params.ngay_den || '';

    let periodText = '';
    switch (analysisType) {
      case 'day':
        periodText = 'ngày';
        break;
      case 'week':
        periodText = 'tuần';
        break;
      case 'month':
        periodText = 'tháng';
        break;
      case 'quarter':
        periodText = 'quý';
        break;
      case 'half-year':
        periodText = '6 tháng';
        break;
      case 'year':
        periodText = 'năm';
        break;
      default:
        periodText = 'kỳ';
    }

    return `Báo cáo bán hàng ${periodCount} ${periodText}${fromDate && toDate ? ` từ ${fromDate} đến ${toDate}` : ''}`;
  }, []);

  const fetchData = useCallback(
    async (params?: SearchFormValues) => {
      setIsLoading(true);
      setError(null);

      try {
        // Call API endpoint for sales report data
        const response = await api.get('/bao-cao/ban-hang/nhieu-ky', {
          params: {
            // Date range
            ngay_tu: params?.ngay_tu,
            ngay_den: params?.ngay_den,

            // Report configuration
            bao_cao_theo: params?.bao_cao_theo,
            phan_tich_theo: params?.phan_tich_theo,
            so_ky_phan_tich: params?.so_ky_phan_tich,

            // Customer filters
            ma_kh: params?.ma_kh,
            nhom_kh_1: params?.nhom_kh_1,
            nhom_kh_2: params?.nhom_kh_2,
            nhom_kh_3: params?.nhom_kh_3,
            khu_vuc: params?.khu_vuc,
            ma_nv_ban_hang: params?.ma_nv_ban_hang,

            // Product filters
            ma_vt: params?.ma_vt,
            nhom_vt_1: params?.nhom_vt_1,
            nhom_vt_2: params?.nhom_vt_2,
            nhom_vt_3: params?.nhom_vt_3,
            ma_kho: params?.ma_kho,

            // Filter by object
            ma_bp: params?.ma_bp,
            ma_vv: params?.ma_vv,
            ma_hd: params?.ma_hd,
            ma_dot_tt: params?.ma_dot_tt,
            ma_khe_uoc: params?.ma_khe_uoc,
            ma_phi: params?.ma_phi,
            ma_sp: params?.ma_sp,
            ma_lenh_sx: params?.ma_lenh_sx,
            ma_chi_phi_khong_hop_le: params?.ma_chi_phi_khong_hop_le,

            // Other filters
            ma_ct: params?.ma_ct,
            ma_lo: params?.ma_lo,
            ma_vi_tri: params?.ma_vi_tri,
            tk_hang: params?.tk_hang,
            tk_doanh_thu: params?.tk_doanh_thu,
            tk_gia_von: params?.tk_gia_von,

            // Additional filters
            dien_giai: params?.dien_giai,
            tu_so_ct: params?.tu_so_ct,
            den_so_ct: params?.den_so_ct
          }
        });

        // Extract data from API response
        const apiData = response.data?.data || [];

        // If API returns empty data, fallback to mock data for development
        let reportData: SalesReportItem[];
        if (apiData.length === 0) {
          console.warn('API returned empty data, using mock data for development');
          const mockData = generateMockSalesReportData();
          const summaryRow = generateSummaryRow(mockData);
          reportData = [...mockData, summaryRow];
        } else {
          // Transform API data to match expected format
          reportData = apiData.map((item: any, index: number) => ({
            id: item.id || `row-${index}`,
            ma: item.ma || item.code || '',
            ten: item.ten || item.name || '',
            sl: item.sl || item.so_luong || item.quantity || 0,
            disabled: item.disabled || false
          }));
        }

        // Transform data based on search parameters
        const transformedData = transformDataForPeriods(reportData, params);

        // Generate columns based on search parameters
        const columns = generateDynamicColumns(params);

        const tableData: TableData = {
          name: getTableTitle(params),
          columns,
          rows: transformedData
        };

        setTables([tableData]);
      } catch (err) {
        console.error('Error fetching sales report data:', err);

        // Fallback to mock data on API error
        console.warn('API call failed, using mock data as fallback');
        const mockData = generateMockSalesReportData();
        const summaryRow = generateSummaryRow(mockData);
        const allData = [...mockData, summaryRow];

        // Transform data based on search parameters
        const transformedData = transformDataForPeriods(allData, params);

        // Generate columns based on search parameters
        const columns = generateDynamicColumns(params);

        const tableData: TableData = {
          name: getTableTitle(params),
          columns,
          rows: transformedData
        };

        setTables([tableData]);
        setError(err as Error);
      } finally {
        setIsLoading(false);
      }
    },
    [generateDynamicColumns, transformDataForPeriods, getTableTitle]
  );

  // UI Event Handlers
  const handleInitialSearchClose = useCallback(() => {
    setInitialSearchDialogOpen(false);
  }, []);

  const handleInitialSearch = useCallback(
    (data: SearchFormValues) => {
      setSearchParams(data);
      setShowTable(true);
      setInitialSearchDialogOpen(false);
      fetchData(data);
    },
    [fetchData]
  );

  const handleSearchClick = useCallback(() => {
    setInitialSearchDialogOpen(true);
  }, []);

  const handleEditPrintTemplateClick = useCallback(() => {
    setEditPrintTemplateDialogOpen(true);
  }, []);

  const handleClosePrintTemplateDialog = useCallback(() => {
    setEditPrintTemplateDialogOpen(false);
  }, []);

  const handleSavePrintTemplate = useCallback((template: any) => {
    console.log('Print template saved:', template);
    setEditPrintTemplateDialogOpen(false);
  }, []);

  const handleRowClick = useCallback((row: any) => {
    console.log('Row clicked:', row);
    setSelectedRow(row);
  }, []);

  const handleRefreshClick = useCallback(() => {
    fetchData(searchParams);
  }, [fetchData, searchParams]);

  const handleFixedColumnsClick = useCallback(() => {
    console.log('Fixed columns clicked');
  }, []);

  const handleExportDataClick = useCallback(() => {
    console.log('Export data clicked');
  }, []);

  return {
    initialSearchDialogOpen,
    editPrintTemplateDialogOpen,
    showTable,
    searchParams,
    tables,
    isLoading,
    error,

    // Event handlers
    handleInitialSearchClose,
    handleInitialSearch,
    handleSearchClick,
    handleEditPrintTemplateClick,
    handleClosePrintTemplateDialog,
    handleSavePrintTemplate,
    handleRowClick,
    handleRefreshClick,
    handleFixedColumnsClick,
    handleExportDataClick
  };
}
