import React, { useState, useEffect } from 'react';
import { generateFirstTwoColumnHeaders, generateThirdColumnHeader } from '../utils/dateCalculations';
import { generateMockSalesReportData, generateSummaryRow, SalesReportItem } from '../mock-data/data';
import { TableData } from '@/components/custom/arito/data-tables/types';

import { SearchFormValues } from '../schema';

export interface UseSalesReportReturn {
  tables: TableData[];
  handleRowClick: (params: any) => void;
  isLoading: boolean;
  error: Error | null;
  refreshData: () => Promise<void>;
  selectedIds: string[];
  handleCheckboxChange: (id: string) => void;
}

export function useSalesReport(searchParams?: SearchFormValues): UseSalesReportReturn {
  const [tables, setTables] = useState<TableData[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);

  // Generate dynamic column headers based on report option and analysis period
  const generateDynamicColumns = (searchParams?: SearchFormValues) => {
    if (!searchParams?.bao_cao_theo || !searchParams?.phan_tich_theo) {
      // Return default 3 columns if no search params
      return [
        { field: 'ma', headerName: 'Mã', width: 150 },
        { field: 'ten', headerName: 'Tên', width: 300, flex: 1 },
        {
          field: 'sl',
          headerName: 'Số lượng',
          width: 120,
          type: 'number' as const,
          align: 'right' as const,
          headerAlign: 'right' as const
        }
      ];
    }

    // Generate headers based on report option and analysis type
    const [firstHeader, secondHeader] = generateFirstTwoColumnHeaders(searchParams.bao_cao_theo);
    const thirdHeader = generateThirdColumnHeader(searchParams.phan_tich_theo);

    return [
      {
        field: 'ma',
        headerName: firstHeader,
        width: 150
      },
      {
        field: 'ten',
        headerName: secondHeader,
        width: 300,
        flex: 1
      },
      {
        field: 'sl',
        headerName: thirdHeader,
        width: 120,
        type: 'number' as const,
        align: 'right' as const,
        headerAlign: 'right' as const
      }
    ];
  };

  const fetchData = async (params?: SearchFormValues) => {
    setIsLoading(true);
    setError(null);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Generate mock data
      const mockData = generateMockSalesReportData();
      const summaryRow = generateSummaryRow(mockData);
      const allData = [...mockData, summaryRow];

      // Transform data based on search parameters
      const transformedData = transformDataForPeriods(allData, params);

      // Generate columns based on search parameters
      const columns = generateDynamicColumns(params);

      const tableData: TableData = {
        name: getTableTitle(params),
        columns,
        rows: transformedData
      };

      setTables([tableData]);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  };

  const transformDataForPeriods = (data: SalesReportItem[], params?: SearchFormValues) => {
    if (!params?.phan_tich_theo || !params?.so_ky_phan_tich) {
      return data;
    }

    // Transform the data to match the backend API structure: ["ma", "ten", "sl"]
    return data.map(item => ({
      id: item.id,
      ma: item.ma,
      ten: item.ten,
      sl: item.sl,
      disabled: item.disabled
    }));
  };

  const getTableTitle = (params?: SearchFormValues): string => {
    if (!params) return 'Báo cáo bán hàng nhiều kỳ';

    const analysisType = params.phan_tich_theo || 'month';
    const periodCount = params.so_ky_phan_tich || 12;
    const fromDate = params.ngay_tu || '';
    const toDate = params.ngay_den || '';

    let periodText = '';
    switch (analysisType) {
      case 'day':
        periodText = 'ngày';
        break;
      case 'week':
        periodText = 'tuần';
        break;
      case 'month':
        periodText = 'tháng';
        break;
      case 'quarter':
        periodText = 'quý';
        break;
      case 'half-year':
        periodText = '6 tháng';
        break;
      case 'year':
        periodText = 'năm';
        break;
      default:
        periodText = 'kỳ';
    }

    return `Báo cáo bán hàng ${periodCount} ${periodText}${fromDate && toDate ? ` từ ${fromDate} đến ${toDate}` : ''}`;
  };

  const refreshData = async () => {
    await fetchData(searchParams);
  };

  const handleRowClick = (params: any) => {
    console.log('Row clicked:', params);
  };

  const handleCheckboxChange = (id: string) => {
    setSelectedIds(prev => (prev.includes(id) ? prev.filter(selectedId => selectedId !== id) : [...prev, id]));
  };

  // Load data when searchParams change
  useEffect(() => {
    if (searchParams) {
      fetchData(searchParams);
    }
  }, [searchParams]);

  return {
    tables,
    handleRowClick,
    isLoading,
    error,
    refreshData,
    selectedIds,
    handleCheckboxChange
  };
}
