import React from 'react';
import {
  groupColumns,
  regionColumns,
  customerSearchColumns,
  vatTuSearchColumns,
  warehouseSearchColumns
} from '@/constants/search-columns';
import { productTypeOptions, reportTemplateOptions } from '../../fieldConstraints';
import { FormField } from '@/components/custom/arito/form/form-field';
import { Label } from '@/components/ui/label';

interface DetailsTabProps {}

const DetailsTab: React.FC<DetailsTabProps> = () => {
  return (
    <div className='w-[800px] min-w-[800px] space-y-2 p-4'>
      <div className='flex flex-col space-y-1'>
        {/* 1. Mã khách hàng */}
        <div className='flex items-center'>
          <FormField
            name='ma_kh'
            label='Mã khách hàng'
            labelClassName='w-40'
            type='text'
            withSearch
            searchEndpoint='/api/customers'
            searchResultLabelKey='customer_name'
            searchResultValueKey='customer_code'
            searchColumns={customerSearchColumns}
            className='w-[400px]'
          />
        </div>

        {/* 2. Nhóm khách hàng */}
        <div className='flex items-center pt-1'>
          <Label className='w-40 min-w-40'>Nhóm khách hàng:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='nhom_kh_1'
                label=''
                type='text'
                withSearch
                searchEndpoint='/api/groups?type=customer&level=1'
                searchResultLabelKey='ten_phan_nhom'
                searchResultValueKey='ma_phan_nhom'
                searchColumns={groupColumns}
                className='w-40'
              />
              <FormField
                name='nhom_kh_2'
                label=''
                type='text'
                withSearch
                searchEndpoint='/api/groups?type=customer&level=2'
                searchResultLabelKey='ten_phan_nhom'
                searchResultValueKey='ma_phan_nhom'
                searchColumns={groupColumns}
                className='w-40'
              />
              <FormField
                name='nhom_kh_3'
                label=''
                type='text'
                withSearch
                searchEndpoint='/api/groups?type=customer&level=3'
                searchResultLabelKey='ten_phan_nhom'
                searchResultValueKey='ma_phan_nhom'
                searchColumns={groupColumns}
                className='w-40'
              />
            </div>
          </div>
        </div>

        {/* 3. Khu vực */}
        <div className='mb-4'>
          <FormField
            name='khu_vuc'
            label='Khu vực'
            labelClassName='w-40'
            type='text'
            withSearch
            searchEndpoint='/api/regions'
            searchResultLabelKey='rgname'
            searchResultValueKey='rgcode'
            searchColumns={regionColumns}
            className='w-[400px]'
          />
        </div>

        {/* 4. Mã vật tư */}
        <div className='flex items-center'>
          <FormField
            name='ma_vt'
            label='Mã vật tư'
            labelClassName='w-40'
            type='text'
            withSearch
            searchEndpoint='/api/products'
            searchResultLabelKey='ten_vt'
            searchResultValueKey='ma_vt'
            searchColumns={vatTuSearchColumns}
            className='w-[400px]'
          />
        </div>

        {/* 5. Nhóm vật tư */}
        <div className='flex items-center pt-1'>
          <Label className='w-40 min-w-40'>Nhóm vật tư:</Label>
          <div className='flex-1'>
            <div className='flex gap-1'>
              <FormField
                name='nhom_vt_1'
                label=''
                type='text'
                withSearch
                searchEndpoint='/api/groups?type=product&level=1'
                searchResultLabelKey='ten_phan_nhom'
                searchResultValueKey='ma_phan_nhom'
                searchColumns={groupColumns}
                className='w-40'
              />
              <FormField
                name='nhom_vt_2'
                label=''
                type='text'
                withSearch
                searchEndpoint='/api/groups?type=product&level=2'
                searchResultLabelKey='ten_phan_nhom'
                searchResultValueKey='ma_phan_nhom'
                searchColumns={groupColumns}
                className='w-40'
              />
              <FormField
                name='nhom_vt_3'
                label=''
                type='text'
                withSearch
                searchEndpoint='/api/groups?type=product&level=3'
                searchResultLabelKey='ten_phan_nhom'
                searchResultValueKey='ma_phan_nhom'
                searchColumns={groupColumns}
                className='w-40'
              />
            </div>
          </div>
        </div>

        {/* 6. Mã kho */}
        <div className='flex items-center'>
          <FormField
            name='ma_kho'
            label='Mã kho'
            labelClassName='w-40'
            type='text'
            withSearch
            searchEndpoint='/api/warehouses'
            searchResultLabelKey='ten_kho'
            searchResultValueKey='ma_kho'
            searchColumns={warehouseSearchColumns}
            className='w-[400px]'
          />
        </div>

        <div className='flex items-center gap-2'>
          <FormField
            name='loai_vt'
            label='Loại vật tư'
            labelClassName='w-40'
            type='select'
            options={productTypeOptions}
            className='w-[400px]'
          />
          <FormField
            name='theo_doi_ton_kho'
            label='Theo dõi tồn kho'
            labelClassName='w-40'
            type='checkbox'
            className='w-[400px]'
          />
        </div>

        <div className='flex items-center'>
          <FormField
            name='mau_bao_cao'
            label='Mẫu báo cáo'
            labelClassName='w-40'
            type='select'
            options={reportTemplateOptions}
            className='w-[400px]'
          />
        </div>
      </div>
    </div>
  );
};

export default DetailsTab;
