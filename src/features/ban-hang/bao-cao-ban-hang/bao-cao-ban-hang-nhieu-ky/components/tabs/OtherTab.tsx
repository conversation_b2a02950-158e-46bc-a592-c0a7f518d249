import React, { useState } from 'react';
import { accountSearchColumns, giaoDichSearchColumns, loSearchColumns } from '@/constants/search-columns';
import RadixHoverDropdown from '@/components/custom/arito/hover-dropdown/radix-hover-dropdown';
import { SearchField } from '@/components/custom/arito/form/custom-search-field';
import SaveTemplateDialog, { SaveTemplateFormData } from '../SaveTemplateDialog';
import { FormField } from '@/components/custom/arito/form/form-field';
import { SearchDialogState, SearchDialogActions } from '../../hooks';
import DocumentNumberRange from '../DocumentNumberRange';
import { AccountModel } from '@/types/schemas';
import { Label } from '@/components/ui/label';
import { QUERY_KEYS } from '@/constants';

interface OtherTabProps {
  searchState: {
    state: SearchDialogState;
    actions: SearchDialogActions;
  };
}

const OtherTab: React.FC<OtherTabProps> = ({ searchState: { state, actions } }) => {
  const [saveFilterTemplateDialogOpen, setSaveFilterTemplateDialogOpen] = useState(false);

  const handleSaveFilterTemplate = (data: SaveTemplateFormData) => {
    console.log('Saving filter template:', data);
    setSaveFilterTemplateDialogOpen(false);
  };

  return (
    <div className='space-y-2 p-4' style={{ width: '800px', minWidth: '800px' }}>
      <div className='flex flex-col space-y-1'>
        <div className='flex items-center gap-1'>
          <Label className='w-40 min-w-40'>Mã giao dịch:</Label>
          <div className='w-[57.5%]'>
            <SearchField<any>
              displayRelatedField={`ma_gd`}
              columnDisplay={`ma_gd`}
              searchEndpoint={`/api/transactions`}
              searchColumns={giaoDichSearchColumns}
              value={state.giaoDich?.ma_gd || ''}
              relatedFieldValue={state.giaoDich?.ten_gd || ''}
              onRowSelection={actions.setGiaoDich}
            />
          </div>
        </div>

        <div className='flex items-center gap-1'>
          <Label className='w-40 min-w-40'>Tài khoản vật tư:</Label>
          <div className='w-[57.5%]'>
            <SearchField<AccountModel>
              displayRelatedField={`name`}
              columnDisplay={`code`}
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
              searchColumns={accountSearchColumns}
              value={state.taiKhoanHang?.code || ''}
              relatedFieldValue={state.taiKhoanHang?.name || ''}
              onRowSelection={actions.setTaiKhoanHang}
            />
          </div>
        </div>

        <div className='flex items-center gap-1'>
          <Label className='w-40 min-w-40'>Tài khoản doanh thu:</Label>
          <div className='w-[57.5%]'>
            <SearchField<AccountModel>
              displayRelatedField={`name`}
              columnDisplay={`code`}
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
              searchColumns={accountSearchColumns}
              value={state.taiKhoanDoanhThu?.code || ''}
              relatedFieldValue={state.taiKhoanDoanhThu?.name || ''}
              onRowSelection={actions.setTaiKhoanDoanhThu}
            />
          </div>
        </div>

        <div className='flex items-center gap-1'>
          <Label className='w-40 min-w-40'>Tài khoản giá vốn:</Label>
          <div className='w-[57.5%]'>
            <SearchField<AccountModel>
              displayRelatedField={`name`}
              columnDisplay={`code`}
              searchEndpoint={`/${QUERY_KEYS.TAI_KHOAN}`}
              searchColumns={accountSearchColumns}
              value={state.taiKhoanGiaVon?.code || ''}
              relatedFieldValue={state.taiKhoanGiaVon?.name || ''}
              onRowSelection={actions.setTaiKhoanGiaVon}
            />
          </div>
        </div>

        <div className='flex items-center gap-1'>
          <Label className='w-40 min-w-40'>Mã lô:</Label>
          <div className='w-[57.5%]'>
            <SearchField<any>
              displayRelatedField={`ten_lo`}
              columnDisplay={`ma_lo`}
              searchEndpoint={`/${QUERY_KEYS.LO}`}
              searchColumns={loSearchColumns}
              value={state.maLo?.ma_lo || ''}
              relatedFieldValue={state.maLo?.ten_lo || ''}
              onRowSelection={actions.setMaLo}
            />
          </div>
        </div>

        <div className='flex items-center gap-1'>
          <Label className='w-40 min-w-40'>Mã vị trí:</Label>
          <div className='w-[57.5%]'>
            <SearchField<any>
              displayRelatedField={`ten_vi_tri`}
              columnDisplay={`ma_vi_tri`}
              searchEndpoint={`/api/locations`}
              searchColumns={[
                { field: 'ma_vi_tri', headerName: 'Mã vị trí', width: 120 },
                { field: 'ten_vi_tri', headerName: 'Tên vị trí', width: 200 }
              ]}
              value={state.viTri?.ma_vi_tri || ''}
              relatedFieldValue={state.viTri?.ten_vi_tri || ''}
              onRowSelection={actions.setViTri}
            />
          </div>
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Số c/từ (từ/đến):</Label>
          <DocumentNumberRange fromDocumentNumberName='fromDocumentNumber' toDocumentNumberName='toDocumentNumber' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Diễn giải:</Label>
          <FormField name='description' label='' type='text' className='w-32' />
        </div>

        <div className='flex items-center'>
          <Label className='w-40 min-w-40'>Mẫu lọc báo cáo:</Label>
          <div className='flex items-center gap-2'>
            <FormField
              name='reportFilterTemplate'
              label=''
              type='select'
              options={[{ value: 'user_filter', label: 'Người dùng tự lọc' }]}
              className='w-48'
            />

            <div className='h-9 w-9 flex-shrink-0'>
              {/* Fixed size container */}
              <RadixHoverDropdown
                iconNumber={624}
                items={[
                  {
                    value: 'save_new',
                    label: 'Lưu mẫu mới',
                    icon: 7,
                    onClick: () => setSaveFilterTemplateDialogOpen(true)
                  },
                  {
                    value: 'save_overwrite',
                    label: 'Lưu đè vào mẫu đang chọn',
                    icon: 75,
                    onClick: () => console.log('Overwrite current filter template')
                  },
                  {
                    value: 'delete',
                    label: 'Xóa mẫu đang chọn',
                    icon: 8,
                    onClick: () => console.log('Delete current filter template')
                  }
                ]}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Save Filter Template Dialog */}
      <SaveTemplateDialog
        open={saveFilterTemplateDialogOpen}
        onClose={() => setSaveFilterTemplateDialogOpen(false)}
        onSave={handleSaveFilterTemplate}
        templateType='filter'
      />
    </div>
  );
};

export default OtherTab;
