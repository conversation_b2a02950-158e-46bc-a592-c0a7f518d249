import React, { useState } from 'react';

import { searchSchema, initialSearchValues, SearchFormValues } from '../schema';
import { DetailsTab, FilterByObjectTab, OtherTab, BasicInfo } from './tabs';
import { AritoDialog } from '@/components/custom/arito/dialog';
import { AritoHeaderTabs } from '@/components/custom/arito';
import { AritoForm } from '@/components/custom/arito/form';
import AritoIcon from '@/components/custom/arito/icon';
import { BottomBar } from '@/components/custom/arito';
import { useSearchDialogState } from '../hooks';

interface InitialSearchDialogProps {
  open: boolean;
  onClose: () => void;
  onSearch: (values: any) => void;
}

export const InitialSearchDialog: React.FC<InitialSearchDialogProps> = ({ open, onClose, onSearch }) => {
  const { state, actions } = useSearchDialogState();

  const handleSubmit = (data: SearchFormValues) => {
    // Merge form data with SearchField states following bang_ke_chung_tu pattern
    const mergedData = {
      ...data,
      ma_nv_ban_hang: state.nhanVienBanHang?.uuid || '',
      ma_kh: state.khachHang?.uuid || '',
      nhom_kh_1: state.nhomKhachHang1?.uuid || '',
      nhom_kh_2: state.nhomKhachHang2?.uuid || '',
      nhom_kh_3: state.nhomKhachHang3?.uuid || '',
      khu_vuc: state.khuVuc?.uuid || '',
      ma_vt: state.vatTu?.uuid || '',
      nhom_vt_1: state.nhomVatTu1?.uuid || '',
      nhom_vt_2: state.nhomVatTu2?.uuid || '',
      nhom_vt_3: state.nhomVatTu3?.uuid || '',
      ma_kho: state.khoHang?.uuid || '',
      ma_bp: state.boPhan?.uuid || '',
      ma_vv: state.vuViec?.uuid || '',
      ma_hd: state.hopDong?.uuid || '',
      ma_dot_tt: state.dotThanhToan?.uuid || '',
      ma_khe_uoc: state.kheUoc?.uuid || '',
      ma_phi: state.phi?.uuid || '',
      ma_sp: state.sanPham?.uuid || '',
      ma_lenh_sx: state.lenhSanXuat?.uuid || '',
      ma_cp_khl: state.chiPhiKhongHopLe?.uuid || '',
      ma_gd: state.giaoDich?.uuid || '',
      ma_lo: state.maLo?.uuid || '',
      ma_vi_tri: state.viTri?.uuid || '',
      tk_hang: state.taiKhoanHang?.uuid || '',
      tk_doanh_thu: state.taiKhoanDoanhThu?.uuid || '',
      tk_gia_von: state.taiKhoanGiaVon?.uuid || ''
    };
    onSearch(mergedData);
    onClose();
  };

  return (
    <AritoDialog
      open={open}
      onClose={onClose}
      title='Báo cáo bán hàng nhiều kỳ'
      maxWidth='lg'
      disableBackdropClose={false}
      disableEscapeKeyDown={false}
      titleIcon={<AritoIcon icon={12} />}
    >
      <AritoForm
        mode='add'
        hasAritoActionBar={false}
        schema={searchSchema}
        initialData={initialSearchValues}
        onSubmit={handleSubmit}
        className='w-full'
        headerFields={
          <div className='max-h-[calc(100vh-150px)] w-[800px] min-w-[800px] overflow-y-auto'>
            <BasicInfo />

            <AritoHeaderTabs
              tabs={[
                {
                  id: 'details',
                  label: 'Chi tiết',
                  component: <DetailsTab searchState={{ state, actions }} />
                },
                {
                  id: 'filter-by-object',
                  label: 'Lọc theo đối tượng',
                  component: <FilterByObjectTab searchState={{ state, actions }} />
                },
                {
                  id: 'other',
                  label: 'Khác',
                  component: <OtherTab searchState={{ state, actions }} />
                }
              ]}
            />
          </div>
        }
        classNameBottomBar='relative w-full flex justify-end gap-2'
        bottomBar={<BottomBar mode='add' onClose={onClose} />}
      />
    </AritoDialog>
  );
};

export default InitialSearchDialog;
